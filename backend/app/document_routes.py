# -*- coding: utf-8 -*-
"""
文档管理路由
处理招标文件的上传、查询等操作
"""

from flask import Blueprint, request, jsonify, current_app, send_from_directory
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import User, Company, TenderDocument, FileStorage
from app.file_service import file_storage_service
from app.information_service import information_service
import os
import uuid
import json
from datetime import datetime

document_bp = Blueprint('document', __name__, url_prefix='/api/documents')

def allowed_file(filename):
    """检查文件类型是否允许"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx', 'ppt', 'pptx'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_file_type(filename):
    """根据文件扩展名获取文件类型"""
    if not filename:
        return 'unknown'
    
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    type_mapping = {
        'pdf': 'pdf',
        'doc': 'doc',
        'docx': 'docx',
        'txt': 'txt',
        'xls': 'excel',
        'xlsx': 'excel',
        'ppt': 'powerpoint',
        'pptx': 'powerpoint'
    }
    
    return type_mapping.get(ext, 'unknown')

@document_bp.route('/tender', methods=['POST'])
@jwt_required()
def upload_tender_document():
    """上传招标文件"""
    user_id = get_jwt_identity()
    
    if 'file' not in request.files:
        return jsonify({'error': '没有选择文件'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': '不支持的文件类型'}), 400
    
    # 获取标题（可选）
    title = request.form.get('title', '').strip()
    if not title:
        title = file.filename  # 如果没有提供标题，使用文件名
    
    try:
        # 使用文件存储服务保存文件
        file_record, is_new = file_storage_service.save_file(file)
        
        # 检查是否已存在相同的招标文件
        existing_tender = TenderDocument.query.filter_by(file_id=file_record.id).first()
        if existing_tender:
            return jsonify({
                'msg': '该招标文件已存在',
                'document': existing_tender.to_dict()
            }), 200
        
        # 创建招标文件记录
        tender_doc = TenderDocument(
            title=title,
            file_id=file_record.id
        )
        
        db.session.add(tender_doc)
        db.session.commit()
        
        # TODO: 解析招标文件内容并保存到信息表
        # parsed_info = parse_tender_document(file_record)
        # information_service.batch_set_tender_information(tender_doc.id, parsed_info)
        
        return jsonify({
            'msg': '招标文件上传成功',
            'document': tender_doc.to_dict(),
            'is_new_file': is_new
        }), 201
        
    except Exception as e:
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@document_bp.route('/tender', methods=['GET'])
@jwt_required()
def get_tender_documents():
    """获取招标文件列表"""
    user_id = get_jwt_identity()
    
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '').strip()
    
    # 构建查询
    query = TenderDocument.query
    
    # 搜索过滤
    if search:
        query = query.filter(TenderDocument.title.contains(search))
    
    # 分页
    pagination = query.order_by(TenderDocument.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'documents': [doc.to_dict() for doc in pagination.items],
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page,
        'per_page': per_page
    })

@document_bp.route('/tender/<int:doc_id>', methods=['GET'])
@jwt_required()
def get_tender_document(doc_id):
    """获取招标文件详情"""
    user_id = get_jwt_identity()
    
    doc = TenderDocument.query.filter_by(id=doc_id).first()
    if not doc:
        return jsonify({'error': '招标文件不存在'}), 404
    
    return jsonify(doc.to_dict())

@document_bp.route('/download/<path:filename>')
@jwt_required()
def download_file(filename):
    """下载文件"""
    # TODO: 添加权限验证
    upload_folder = current_app.config['UPLOAD_FOLDER']
    return send_from_directory(upload_folder, filename, as_attachment=True)
