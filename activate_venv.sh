#!/bin/bash

# Quick script to activate the virtual environment with clean PATH
# Usage: source activate_venv.sh

echo "Activating virtual environment with clean PATH..."

# Reset PATH to avoid pyenv interference
export PATH='/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games'

# Activate the virtual environment
source backend/.venv/bin/activate

echo "Virtual environment activated!"
echo "Python: $(which python)"
echo "Python version: $(python --version)"
echo ""
echo "You can now run Python commands with the correct environment."
echo "To deactivate, run: deactivate"
